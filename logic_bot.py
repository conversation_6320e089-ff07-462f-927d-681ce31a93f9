from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium import webdriver
import pandas as pd
import time
import logging
import os
import datetime
import re

# Configuração de log
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('Prospector.log')
    ]
)

logger = logging.getLogger('Prospector')

def buscar_cep(driver, localizacao):
    """
    Busca uma localização (estado + bairro) no Google Maps
    Função modificada para buscar as mesmas informações que estão sendo buscadas na ui.py

    Args:
        driver: WebDriver do Selenium
        localizacao: String contendo a localização a ser buscada (ex: "Vila Madalena, São Paulo (SP)")
    """
    try:
        logger.info(f"Buscando pela localização: {localizacao}")

        # Limpa o texto prévio e espera pelo campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()

        # Insere a localização no campo de busca (estado + bairro)
        search_box.send_keys(localizacao)
        search_box.send_keys(Keys.ENTER)

        # Espera para carregar os resultados
        logger.info("Aguardando resultados da localização...")
        time.sleep(5)

        # Verifica se a localização foi encontrada - múltiplas estratégias
        try:
            # Estratégia 1: Verificar se há um título de localização
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 1)")
                return True
            except:
                pass

            # Estratégia 2: Verificar se o mapa carregou com a localização
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@role="main"]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 2)")
                return True
            except:
                pass

            # Estratégia 3: Verificar se há algum resultado na página
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@id="pane"]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 3)")
                return True
            except:
                pass

            # Se nenhuma estratégia funcionou, mas não houve erro crítico
            logger.warning(f"Não foi possível confirmar se a localização {localizacao} foi encontrada, mas continuando...")
            return True  # Assume que a localização foi encontrada mesmo sem confirmação

        except Exception as verification_error:
            logger.warning(f"Erro na verificação da localização {localizacao}: {str(verification_error)}")
            return True  # Continua mesmo com erro de verificação

    except Exception as e:
        logger.error(f'Falha ao buscar localização {localizacao}: {str(e)}')
        return False

def buscar_localizacao(driver, localizacao):
    """
    Busca uma localização (estado + bairro) no Google Maps

    Args:
        driver: WebDriver do Selenium
        localizacao: String contendo a localização a ser buscada (ex: "Vila Madalena, São Paulo (SP)")
    """
    try:
        logger.info(f"Buscando pela localização: {localizacao}")

        # Limpa o texto prévio e espera pelo campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()

        # Insere a localização no campo de busca
        search_box.send_keys(localizacao)
        search_box.send_keys(Keys.ENTER)

        # Espera para carregar os resultados
        logger.info("Aguardando resultados da localização...")
        time.sleep(5)

        # Verifica se a localização foi encontrada - múltiplas estratégias
        try:
            # Estratégia 1: Verificar se há um título de localização
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 1)")
                return True
            except:
                pass

            # Estratégia 2: Verificar se o mapa carregou com a localização
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@role="main"]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 2)")
                return True
            except:
                pass

            # Estratégia 3: Verificar se há algum resultado na página
            try:
                WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, '//div[@id="pane"]'))
                )
                logger.info(f"Localização {localizacao} encontrada com sucesso (método 3)")
                return True
            except:
                pass

            # Se nenhuma estratégia funcionou, mas não houve erro crítico
            logger.warning(f"Não foi possível confirmar se a localização {localizacao} foi encontrada, mas continuando...")
            return True  # Assume que a localização foi encontrada mesmo sem confirmação

        except Exception as verification_error:
            logger.warning(f"Erro na verificação da localização {localizacao}: {str(verification_error)}")
            return True  # Continua mesmo com erro de verificação

    except Exception as e:
        logger.error(f'Falha ao buscar localização {localizacao}: {str(e)}')
        return False

def buscar_palavra_chave(driver, palavra_chave):
    """
    Busca uma palavra-chave no Google Maps na região atual

    Args:
        driver: WebDriver do Selenium
        palavra_chave: String contendo o termo a ser buscado
    """
    try:
        logger.info(f"Buscando pela palavra-chave: {palavra_chave}")

        # Espera pela presença do campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )

        # Limpa o texto anterior
        search_box.send_keys(Keys.CONTROL + "a")
        search_box.send_keys(Keys.DELETE)

        # Insere a palavra-chave
        search_box.send_keys(palavra_chave)
        search_box.send_keys(Keys.ENTER)

        # Aguarda pelo carregamento dos resultados
        logger.info("Aguardando resultados da busca...")
        time.sleep(5)

        # Verifica se há resultados
        try:
            resultados = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
            )
            logger.info(f"Encontrados {len(resultados)} resultados iniciais para '{palavra_chave}'")
            return True
        except:
            logger.warning(f"Não foram encontrados resultados para '{palavra_chave}'")
            return False

    except Exception as e:
        logger.error(f'Falha ao buscar palavra-chave {palavra_chave}: {str(e)}')
        return False

def extrair_clientes(driver, quantidade_desejada, progress_callback):
    """
    Extrai informações de clientes com base nos resultados da busca

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso

    Returns:
        Lista de dicionários contendo informações dos clientes
    """
    clientes_extraidos = []
    clientes_unicos = set()
    contador = 0
    tentativas_sem_novos = 0
    MAX_TENTATIVAS_SEM_NOVOS = 10

    # Obter a palavra-chave atual da busca para uso em caso de recuperação
    try:
        palavra_chave_atual = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]').get_attribute('value')
    except:
        palavra_chave_atual = "Loja"  # Valor padrão caso não consiga obter

    logger.info(f"Iniciando extração de {quantidade_desejada} leads...")

    try:
        # Contador para tentativas consecutivas de encontrar elementos
        tentativas_encontrar_elementos = 0
        MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS = 5

        # Loop principal para extrair clientes
        while contador < quantidade_desejada and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
            # Coleta todos os elementos de resultados visíveis na página
            try:
                elementos = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
                )
                logger.info(f"Encontrados {len(elementos)} elementos na página atual")
                tentativas_encontrar_elementos = 0  # Resetar contador se encontrou elementos
            except:
                logger.warning("Não foi possível encontrar elementos na página atual")
                tentativas_encontrar_elementos += 1

                # Se falhar muitas vezes consecutivas, tenta recarregar a página
                if tentativas_encontrar_elementos >= MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS:
                    logger.warning(f"Falhou {MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS} vezes consecutivas ao tentar encontrar elementos. Tentando recarregar a página.")
                    try:
                        # Recarregar a página e refazer a busca
                        driver.get('https://www.google.com/maps/')
                        time.sleep(3)
                        search_box = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                        )
                        search_box.send_keys(palavra_chave_atual)
                        search_box.send_keys(Keys.ENTER)
                        time.sleep(5)
                        logger.info("Página recarregada após falhas consecutivas")
                        tentativas_encontrar_elementos = 0  # Resetar contador
                    except Exception as reload_error:
                        logger.error(f"Erro ao recarregar a página: {str(reload_error)}")
                        # Se não conseguir recarregar, interrompe a extração
                        break

                scroll_down(driver)
                time.sleep(2)
                continue

            # Se já processamos todos os elementos visíveis, role para baixo para carregar mais
            if contador >= len(elementos):
                logger.info("Rolando para baixo para carregar mais resultados...")
                scroll_down(driver)
                time.sleep(2)
                tentativas_sem_novos += 1
                continue

            # Tenta clicar no elemento para abrir os detalhes - múltiplas estratégias
            clique_sucesso = False
            try:
                logger.info(f"Clicando no resultado {contador + 1}")

                # Estratégia 1: Scroll e clique normal
                try:
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elementos[contador])
                    time.sleep(0.5)
                    elementos[contador].click()
                    time.sleep(2)
                    clique_sucesso = True
                    logger.info(f"Clique realizado com sucesso (método 1) no resultado {contador + 1}")
                except:
                    pass

                # Estratégia 2: JavaScript click
                if not clique_sucesso:
                    try:
                        driver.execute_script("arguments[0].click();", elementos[contador])
                        time.sleep(2)
                        clique_sucesso = True
                        logger.info(f"Clique realizado com sucesso (método 2) no resultado {contador + 1}")
                    except:
                        pass

                # Estratégia 3: ActionChains
                if not clique_sucesso:
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(elementos[contador]).click().perform()
                        time.sleep(2)
                        clique_sucesso = True
                        logger.info(f"Clique realizado com sucesso (método 3) no resultado {contador + 1}")
                    except:
                        pass

                if clique_sucesso:
                    tentativas_sem_novos = 0  # Resetar contador de tentativas se conseguiu clicar
                else:
                    logger.warning(f"Não foi possível clicar no resultado {contador + 1} com nenhum método")
                    contador += 1
                    continue

            except Exception as e:
                logger.warning(f"Erro geral ao tentar clicar no resultado {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Aguardar a página de detalhes carregar
            try:
                # Verificar se a página de detalhes carregou
                WebDriverWait(driver, 10).until(
                    lambda d: d.current_url != 'https://www.google.com/maps/' and '@' in d.current_url
                )
                logger.info(f"Página de detalhes carregada para o resultado {contador + 1}")
            except:
                logger.warning(f"Timeout ao aguardar página de detalhes carregar para resultado {contador + 1}")
                # Continua mesmo assim, pode ser que tenha carregado

            # Extrai as informações do cliente
            try:
                # Extrair o nome - múltiplas estratégias
                nome_cliente = None

                # Estratégia 1: XPath principal
                try:
                    nome_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'))
                    )
                    nome_cliente = nome_elemento.text.strip()
                    logger.info(f"Nome extraído com sucesso (método 1): {nome_cliente}")
                except:
                    pass

                # Estratégia 2: XPath alternativo para o nome
                if not nome_cliente:
                    try:
                        nome_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "DUwDvf")]'))
                        )
                        nome_cliente = nome_elemento.text.strip()
                        logger.info(f"Nome extraído com sucesso (método 2): {nome_cliente}")
                    except:
                        pass

                # Estratégia 3: Buscar qualquer h1 na página
                if not nome_cliente:
                    try:
                        nome_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.TAG_NAME, 'h1'))
                        )
                        nome_cliente = nome_elemento.text.strip()
                        logger.info(f"Nome extraído com sucesso (método 3): {nome_cliente}")
                    except:
                        pass

                # Se não conseguiu extrair o nome, pular este resultado
                if not nome_cliente or nome_cliente == "":
                    logger.warning(f"Não foi possível extrair o nome do resultado {contador + 1}, pulando...")
                    contador += 1
                    continue

                # Pular se o cliente já foi processado
                if nome_cliente in clientes_unicos:
                    logger.info(f"Cliente '{nome_cliente}' já foi processado, pulando...")
                    contador += 1
                    continue

                # Extrair informações adicionais
                cliente = {
                    'nome': nome_cliente,
                    'telefone': "Telefone não disponível",
                    'endereco': "Endereço não disponível",
                    'site': "Site não disponível",
                    'email': "Email não disponível"
                }

                # Função auxiliar para limpar e formatar números de telefone
                def limpar_telefone(texto):
                    # Remover textos específicos que não são telefones
                    if not texto or texto.strip() == "":
                        return "Telefone não disponível"

                    texto = re.sub(r'Enviar para o smartphone', '', texto, flags=re.IGNORECASE)

                    # Extrair apenas dígitos, parênteses, traços e espaços
                    texto_limpo = re.sub(r'[^\d\(\)\s\-\+]', '', texto)

                    # Aplicar regex para extrair apenas o número de telefone
                    telefone_match = re.search(r'(?:\+?55\s?)?(?:\(?\d{2}\)?[\s.-]?)?\d{4,5}[\s.-]?\d{4}', texto_limpo)

                    if telefone_match:
                        # Apenas extrair o número encontrado
                        return telefone_match.group(0)

                    # Se não encontrar um padrão válido de telefone, verificar se pelo menos tem dígitos
                    digitos = re.sub(r'\D', '', texto_limpo)
                    if len(digitos) >= 8:  # Telefone tem pelo menos 8 dígitos
                        # Formatar o número encontrado
                        if len(digitos) >= 10:  # Com DDD
                            return f"({digitos[:2]}) {digitos[2:6]}-{digitos[6:]}"
                        else:  # Sem DDD
                            return f"{digitos[:4]}-{digitos[4:]}"

                    return "Telefone não disponível"

                # Extrair telefone
                try:
                    # Primeira tentativa - capturar do botão com data-item-id específico para telefone
                    telefone_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[@data-item-id[contains(., "phone:tel:")]]'))
                    )
                    # Extrair do aria-label que contém o número formatado
                    aria_label = telefone_elemento.get_attribute('aria-label')
                    if aria_label and "telefone:" in aria_label.lower():
                        # Extrair o número do aria-label
                        telefone_texto = aria_label.split(':', 1)[1].strip()
                        cliente['telefone'] = limpar_telefone(telefone_texto)
                    else:
                        # Capturar do texto do elemento interno
                        try:
                            telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "Io6YTe") or contains(@class, "fontBodyMedium")]')
                            telefone_texto = telefone_interno.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            # Se não encontrar o elemento interno, usar o texto do botão
                            telefone_texto = telefone_elemento.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                except:
                    try:
                        # Segunda tentativa - capturar do botão com classe CsEnBe que tem aria-label contendo "Telefone:"
                        telefone_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, '//button[contains(@class, "CsEnBe") and contains(@aria-label, "Telefone:")]'))
                        )
                        aria_label = telefone_elemento.get_attribute('aria-label')
                        if aria_label:
                            # Extrair o número do aria-label
                            telefone_texto = aria_label.split(':', 1)[1].strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        else:
                            # Tentar capturar da div interna
                            try:
                                telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "rogA2c")]//div[contains(@class, "fontBodyMedium")]')
                                telefone_texto = telefone_interno.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            except:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                    except:
                        try:
                            # Terceira tentativa - capturar de links href="tel:"
                            telefone_elemento = WebDriverWait(driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, '//a[contains(@href, "tel:")]'))
                            )
                            href = telefone_elemento.get_attribute('href')
                            if href and 'tel:' in href:
                                telefone_texto = href.split('tel:')[1].strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            else:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            logger.warning(f"Telefone não disponível para '{nome_cliente}'")
                            cliente['telefone'] = "Telefone não disponível"

                # Extrair endereço (opcional)
                try:
                    endereco_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[contains(@data-item-id, "address")]'))
                    )
                    cliente['endereco'] = endereco_elemento.text.strip()
                except:
                    logger.warning(f"Endereço não disponível para '{nome_cliente}'")

                # Extrair site (opcional)
                try:
                    site_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//a[contains(@data-item-id, "authority")]'))
                    )
                    cliente['site'] = site_elemento.get_attribute('href')
                except:
                    logger.warning(f"Site não disponível para '{nome_cliente}'")

                # Extrair email (opcional)
                try:
                    # Estratégia 1: Buscar por links mailto:
                    try:
                        email_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, '//a[contains(@href, "mailto:")]'))
                        )
                        href = email_elemento.get_attribute('href')
                        if href and 'mailto:' in href:
                            email_texto = href.split('mailto:')[1].strip()
                            cliente['email'] = email_texto
                            logger.info(f"Email encontrado para '{nome_cliente}': {email_texto}")
                        else:
                            raise Exception("Href não contém mailto")
                    except:
                        # Estratégia 2: Buscar por padrões de email no texto da página
                        try:
                            import re
                            page_text = driver.page_source
                            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                            emails_found = re.findall(email_pattern, page_text)

                            # Filtrar emails válidos (excluir emails genéricos/inválidos)
                            valid_emails = []
                            for email in emails_found:
                                if not any(invalid in email.lower() for invalid in ['noreply', 'no-reply', 'example', 'test', 'placeholder']):
                                    valid_emails.append(email)

                            if valid_emails:
                                cliente['email'] = valid_emails[0]  # Pegar o primeiro email válido
                                logger.info(f"Email encontrado via regex para '{nome_cliente}': {valid_emails[0]}")
                            else:
                                raise Exception("Nenhum email válido encontrado")
                        except:
                            logger.warning(f"Email não disponível para '{nome_cliente}'")
                            cliente['email'] = "Email não disponível"
                except Exception as e:
                    logger.warning(f"Erro ao extrair email para '{nome_cliente}': {str(e)}")
                    cliente['email'] = "Email não disponível"

                # Adicionar cliente à lista
                clientes_extraidos.append(cliente)
                clientes_unicos.add(nome_cliente)

                logger.info(f"Lead {contador + 1}: {nome_cliente} - {cliente['telefone']}")

                # Atualizar progresso
                progress_callback(contador + 1, quantidade_desejada, cliente)

                contador += 1

            except Exception as e:
                logger.error(f"Erro ao extrair dados do cliente {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Voltar para a lista de resultados
            tentativas_voltar = 0
            max_tentativas_voltar = 3
            sucesso_voltar = False

            while tentativas_voltar < max_tentativas_voltar and not sucesso_voltar:
                try:
                    # Método 1: Botão voltar
                    voltar_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, '//button[@aria-label="Voltar"]'))
                    )
                    voltar_button.click()
                    time.sleep(1.5)
                    sucesso_voltar = True
                    logger.info("Voltou para a lista de resultados usando o botão Voltar")
                except:
                    try:
                        # Método 2: JavaScript history.back()
                        logger.warning("Não foi possível voltar usando o botão, tentando history.back()")
                        driver.execute_script("history.back()")
                        time.sleep(2)

                        # Verificar se voltou para a lista de resultados
                        elementos_apos_voltar = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
                        if len(elementos_apos_voltar) > 0:
                            sucesso_voltar = True
                            logger.info("Voltou para a lista de resultados usando history.back()")
                        else:
                            # Método 3: Tentar clicar em qualquer área fora do card de detalhes
                            try:
                                logger.warning("Não foi possível voltar com history.back(), tentando clicar fora do card")
                                driver.find_element(By.XPATH, '//div[@class="aomaec"]').click()
                                time.sleep(1)
                                sucesso_voltar = True
                            except:
                                # Método 4: Pressionar ESC
                                try:
                                    logger.warning("Tentando pressionar ESC para fechar o card")
                                    webdriver.ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                                    time.sleep(1)
                                    sucesso_voltar = True
                                except:
                                    # Método 5: Recarregar a página e refazer a busca
                                    if tentativas_voltar == max_tentativas_voltar - 1:  # Última tentativa
                                        logger.warning("Tentando recarregar a página e refazer a busca")
                                        driver.refresh()
                                        time.sleep(3)

                                        # Tentar refazer a busca
                                        try:
                                            search_box = WebDriverWait(driver, 10).until(
                                                EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                                            )
                                            search_box.clear()
                                            search_box.send_keys(Keys.CONTROL + "a")
                                            search_box.send_keys(Keys.DELETE)
                                            search_box.send_keys(driver.current_url.split('@')[1].split(',')[0])  # Usar coordenadas da URL
                                            search_box.send_keys(Keys.ENTER)
                                            time.sleep(3)
                                            sucesso_voltar = True
                                        except:
                                            logger.error("Falha ao recarregar e refazer a busca")
                    except:
                        logger.error("Falha ao navegar de volta para a lista de resultados")

                tentativas_voltar += 1

            # Se não conseguiu voltar após todas as tentativas, tenta continuar mesmo assim
            if not sucesso_voltar:
                logger.error("Não foi possível voltar para a lista de resultados após várias tentativas")
                # Tentar recarregar a página como último recurso
                try:
                    driver.get('https://www.google.com/maps/')
                    time.sleep(3)
                    search_box = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                    )
                    search_box.send_keys(palavra_chave_atual)
                    search_box.send_keys(Keys.ENTER)
                    time.sleep(5)
                    logger.info("Página recarregada e busca refeita como último recurso")
                except:
                    logger.error("Falha ao recarregar a página como último recurso")

    except Exception as e:
        logger.error(f'Falha na extração: {str(e)}')

    finally:
        # Informações finais
        total_extraidos = len(clientes_extraidos)
        logger.info(f"Extração concluída. Total de leads extraídos: {total_extraidos}")

        if total_extraidos < quantidade_desejada:
            logger.warning(f"Atenção: foram solicitados {quantidade_desejada} leads, mas apenas {total_extraidos} foram encontrados.")

        return clientes_extraidos

def scroll_down(driver):
    """
    Rola a página para baixo para carregar mais resultados

    Args:
        driver: WebDriver do Selenium
    """
    try:
        # Tenta encontrar o último elemento visível e rolar até ele
        elementos = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        if elementos:
            driver.execute_script("arguments[0].scrollIntoView({block: 'end'});", elementos[-1])
            # Rola um pouco mais para garantir que novos elementos sejam carregados
            driver.execute_script("window.scrollBy(0, 200);")
        else:
            # Tenta encontrar o painel de resultados e rolar dentro dele
            try:
                painel_resultados = driver.find_element(By.XPATH, '//div[@role="feed"]')
                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollTop + 500", painel_resultados)
            except:
                # Se não encontrar o painel, tenta rolar a página principal
                driver.execute_script("window.scrollBy(0, 500);")

            # Tenta clicar no botão "Ver mais resultados" se existir
            try:
                botao_mais = driver.find_element(By.XPATH, '//button[contains(., "Ver mais")]')
                botao_mais.click()
                logger.info("Clicou no botão 'Ver mais resultados'")
                time.sleep(1)
            except:
                pass
    except Exception as e:
        logger.error(f"Erro ao rolar a página: {str(e)}")
        # Fallback para rolagem simples
        driver.execute_script("window.scrollBy(0, 500);")

    # Aguarda um momento para o carregamento dos elementos
    time.sleep(1)

def df_clientes(clientes_extraidos):
    """
    Converte a lista de clientes para um DataFrame pandas

    Args:
        clientes_extraidos: Lista de dicionários com informações dos clientes

    Returns:
        DataFrame pandas
    """
    df = pd.DataFrame(clientes_extraidos)
    return df

def salvar_clientes(clientes, nome_arquivo):
    """
    Salva os clientes extraídos em um arquivo Excel

    Args:
        clientes: Lista de dicionários com informações dos clientes
        nome_arquivo: Caminho do arquivo onde os dados serão salvos
    """
    try:
        logger.info(f"Salvando {len(clientes)} leads no arquivo: {nome_arquivo}")

        # Criar diretório se não existir
        diretorio = os.path.dirname(nome_arquivo)
        if diretorio and not os.path.exists(diretorio):
            os.makedirs(diretorio)

        # Converter para DataFrame e salvar
        df = df_clientes(clientes)

        # Adicionar timestamp
        hora_extracao = datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        df['Data de Extração'] = hora_extracao

        # Salvar arquivo
        df.to_excel(nome_arquivo, index=False, engine='openpyxl')
        logger.info(f"Arquivo salvo com sucesso: {nome_arquivo}")
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar arquivo {nome_arquivo}: {str(e)}")
        raise e

def salvar_clientes_estilizado(clientes, nome_arquivo, estado, formato='excel'):
    """
    Salva os clientes extraídos em um arquivo Excel ou CSV com formatação estilizada

    Args:
        clientes: Lista de dicionários com informações dos clientes
        nome_arquivo: Caminho do arquivo onde os dados serão salvos
        estado: Estado da busca para incluir nos dados
        formato: 'excel' ou 'csv'
    """
    try:
        logger.info(f"Salvando {len(clientes)} leads no arquivo estilizado: {nome_arquivo}")

        # Criar diretório se não existir
        diretorio = os.path.dirname(nome_arquivo)
        if diretorio and not os.path.exists(diretorio):
            os.makedirs(diretorio)

        # Preparar dados no formato desejado
        dados_formatados = []
        for cliente in clientes:
            dados_formatados.append({
                'NOME': cliente.get('nome', 'Nome não disponível'),
                'NUMERO': cliente.get('telefone', 'Telefone não disponível'),
                'EMAIL': cliente.get('email', 'Email não disponível'),
                'ESTADO': estado
            })

        # Converter para DataFrame
        df = pd.DataFrame(dados_formatados)

        # Salvar no formato especificado
        if formato.lower() == 'excel':
            # Salvar como Excel com formatação
            with pd.ExcelWriter(nome_arquivo, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Leads')

                # Obter a planilha para formatação
                workbook = writer.book
                worksheet = writer.sheets['Leads']

                # Formatação do cabeçalho
                from openpyxl.styles import Font, PatternFill, Alignment
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center")

                # Ajustar largura das colunas
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

        elif formato.lower() == 'csv':
            # Salvar como CSV
            df.to_csv(nome_arquivo, index=False, encoding='utf-8-sig')
        else:
            raise ValueError(f"Formato não suportado: {formato}")

        logger.info(f"Arquivo estilizado salvo com sucesso: {nome_arquivo}")
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar arquivo estilizado {nome_arquivo}: {str(e)}")
        raise e
