# 🚀 Guia de Compilação - Prospector

Este guia explica como transformar o aplicativo Prospector em um executável standalone.

## 📋 Pré-requisitos

- Python 3.8 ou superior
- Windows (testado no Windows 10/11)
- Todas as dependências instaladas (veja requirements.txt)

## 🎯 Métodos de Compilação

### Método 1: Compilação Rápida (RECOMENDADO)

Execute o script de compilação rápida:

```bash
python build_now.py
```

Este método:
- ✅ Instala/atualiza automaticamente o PyInstaller
- ✅ Usa configurações otimizadas
- ✅ Gera um executável único
- ✅ Inclui todos os recursos necessários

### Método 2: Compilação Completa

Para uma compilação mais detalhada com verificações:

```bash
python build_prospector.py
```

Este método:
- ✅ Verifica versão do Python
- ✅ Instala todas as dependências
- ✅ <PERSON><PERSON> builds anteriores
- ✅ Verifica arquivos necessários
- ✅ Compilação otimizada
- ✅ Verificação do executável final

### Método 3: Arquivo Batch (Windows)

Duplo clique no arquivo:

```
build.bat
```

### Método 4: Compilação Segura

Para evitar detecções de antivírus:

```bash
python build_safe_exe.py
```

## 📁 Estrutura de Arquivos Necessários

Certifique-se de que os seguintes arquivos estão presentes:

```
📂 Projeto/
├── 📄 ui.py                    # Arquivo principal
├── 🖼️ logo.png                # Ícone da aplicação
├── 📄 file_version_info.txt    # Informações de versão
├── 📄 logic_bot.py            # Lógica principal
├── 📄 splash_screen.py        # Tela de splash
├── 📄 about_dialog.py         # Diálogo sobre
├── 📄 resource_path.py        # Gerenciador de recursos
├── 📄 requirements.txt        # Dependências
└── 🔧 build_*.py             # Scripts de compilação
```

## ⚙️ Configurações do PyInstaller

O executável é gerado com as seguintes configurações:

- **--onefile**: Arquivo único
- **--windowed**: Sem console
- **--icon**: Ícone personalizado
- **--add-data**: Recursos incluídos
- **--hidden-import**: Imports explícitos para PyQt6, Selenium, etc.
- **--noconfirm**: Sem confirmações
- **--clean**: Limpa cache anterior

## 📦 Resultado da Compilação

Após a compilação bem-sucedida:

```
📂 dist/
└── 🎯 Prospector.exe    # Executável final (~50-100 MB)
```

## 🔧 Solução de Problemas

### Erro: "ui.py não encontrado"
- Certifique-se de estar no diretório correto do projeto

### Erro: "PyInstaller não encontrado"
- Execute: `pip install pyinstaller --upgrade`

### Erro: "Módulo não encontrado"
- Execute: `pip install -r requirements.txt`

### Executável muito grande
- Normal para aplicações PyQt6 + Selenium (50-100 MB)

### Antivírus detecta como vírus
- Use `build_safe_exe.py`
- Adicione exceção no antivírus
- Assine digitalmente o executável (opcional)

## 🎯 Testando o Executável

1. Navegue até a pasta `dist/`
2. Execute `Prospector.exe`
3. Teste todas as funcionalidades:
   - Configuração de parâmetros
   - Captura de leads
   - Exportação para Excel/CSV
   - Interface gráfica

## 📊 Informações Técnicas

- **Tamanho esperado**: 50-100 MB
- **Tempo de compilação**: 2-5 minutos
- **Compatibilidade**: Windows 10/11
- **Dependências incluídas**: PyQt6, Selenium, Pandas, etc.

## 🆘 Suporte

Se encontrar problemas:

1. Verifique se todos os arquivos estão presentes
2. Execute `python build_prospector.py` para diagnóstico completo
3. Verifique os logs de erro
4. Certifique-se de que o Python 3.8+ está instalado

## 🎉 Sucesso!

Após a compilação bem-sucedida, você terá um executável standalone que pode ser distribuído sem necessidade de instalar Python ou dependências no computador de destino.

---

**Dica**: Para distribuição, copie apenas o arquivo `Prospector.exe` da pasta `dist/`. Ele contém tudo que é necessário para executar a aplicação.
