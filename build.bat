@echo off
echo ========================================
echo    COMPILANDO LEAD FINDER
echo ========================================

echo Verificando arquivos...
if not exist ui.py (
    echo ERRO: ui.py nao encontrado!
    pause
    exit /b 1
)

echo Iniciando compilacao...
pyinstaller --onefile --windowed --name=Prospector ui.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo Executavel criado em: dist\Prospector.exe
) else (
    echo.
    echo ========================================
    echo    ERRO NA COMPILACAO!
    echo ========================================
)

pause
