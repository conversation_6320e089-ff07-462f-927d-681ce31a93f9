@echo off
echo ========================================
echo    COMPILANDO PROSPECTOR
echo ========================================

echo Verificando arquivos...
if not exist ui.py (
    echo ERRO: ui.py nao encontrado!
    pause
    exit /b 1
)

if not exist logo.png (
    echo AVISO: logo.png nao encontrado!
)

echo.
echo Atualizando PyInstaller...
python -m pip install pyinstaller --upgrade

echo.
echo Iniciando compilacao otimizada...
pyinstaller --onefile --windowed --name=Prospector --icon=logo.png --add-data=logo.png;. --noconfirm --clean --hidden-import=PyQt6.QtCore --hidden-import=PyQt6.QtGui --hidden-import=PyQt6.QtWidgets --hidden-import=selenium --hidden-import=webdriver_manager --hidden-import=pandas --hidden-import=openpyxl ui.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo Executavel criado em: dist\Prospector.exe

    if exist dist\Prospector.exe (
        for %%A in (dist\Prospector.exe) do (
            set size=%%~zA
            set /a sizeMB=!size!/1024/1024
        )
        echo Tamanho do arquivo: !sizeMB! MB
    )
) else (
    echo.
    echo ========================================
    echo    ERRO NA COMPILACAO!
    echo ========================================
    echo Tente executar: python build_prospector.py
)

echo.
pause
