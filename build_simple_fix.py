#!/usr/bin/env python3
"""
Script de compilação simplificado e corrigido para o Prospector
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def clean_build():
    """<PERSON><PERSON> builds anteriores"""
    print("🧹 Limpando builds anteriores...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Removido: {dir_name}")
            except:
                print(f"⚠️ Não foi possível remover: {dir_name}")
    
    # Remover arquivos .spec
    for spec_file in Path('.').glob('*.spec'):
        try:
            spec_file.unlink()
            print(f"✅ Removido: {spec_file}")
        except:
            pass

def main():
    print("🚀 COMPILADOR SIMPLIFICADO - PROSPECTOR")
    print("=" * 50)
    
    # Verificar arquivo principal
    if not os.path.exists('ui.py'):
        print("❌ Arquivo ui.py não encontrado!")
        return False
    
    # Limpar builds anteriores
    clean_build()
    
    print("\n📦 Atualizando PyInstaller...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "pyinstaller==6.13.0", "--force-reinstall"
        ])
        print("✅ PyInstaller instalado")
    except:
        print("⚠️ Erro ao instalar PyInstaller")
    
    print("\n🔨 Compilando executável (versão básica)...")
    
    # Comando mais simples do PyInstaller
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=Prospector',
        '--noconfirm',
        '--clean',
        'ui.py'
    ]
    
    # Adicionar ícone se existir
    if os.path.exists('logo.png'):
        cmd.insert(-1, '--icon=logo.png')
        cmd.insert(-1, '--add-data=logo.png;.')
        print("✅ Ícone incluído")
    
    print(f"📋 Executando: {' '.join(cmd[:5])}...")
    
    try:
        # Executar com timeout
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Compilação concluída!")
            
            # Verificar se o arquivo foi criado
            exe_path = Path("dist") / "Prospector.exe"
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Executável: {exe_path}")
                print(f"📏 Tamanho: {size_mb:.1f} MB")
                print("\n🎉 SUCESSO!")
                return True
            else:
                print("❌ Executável não encontrado")
                return False
        else:
            print("❌ Erro na compilação:")
            # Mostrar apenas as últimas linhas do erro
            error_lines = result.stderr.split('\n')
            for line in error_lines[-10:]:
                if line.strip():
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação")
        return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("✅ COMPILAÇÃO BEM-SUCEDIDA!")
        print("📂 Execute: dist/Prospector.exe")
    else:
        print("❌ FALHA NA COMPILAÇÃO")
        print("\n💡 Soluções:")
        print("1. Verifique se todas as dependências estão instaladas")
        print("2. Execute: pip install -r requirements.txt")
        print("3. Tente compilar sem ícone")
    
    input("\nPressione Enter para sair...")
