#!/usr/bin/env python3
"""
Script simples para compilar o projeto em executável
"""

import subprocess
import sys
import os

def build_executable():
    """Compila o projeto em executável"""
    try:
        print("🔨 Iniciando compilação do executável...")
        
        # Comando básico do PyInstaller
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=Prospector',
            '--add-data=logo.png;.',
            'ui.py'
        ]
        
        print(f"📋 Executando: {' '.join(cmd)}")
        
        # Executar o comando
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Compilação concluída com sucesso!")
            print(f"📁 Executável criado em: dist/Prospector.exe")
            return True
        else:
            print("❌ Erro na compilação:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 COMPILADOR SIMPLES - LEAD FINDER")
    print("=" * 50)
    
    # Verificar se o arquivo principal existe
    if not os.path.exists('ui.py'):
        print("❌ Arquivo ui.py não encontrado!")
        return False
    
    # Verificar se o logo existe
    if not os.path.exists('logo.png'):
        print("⚠️ Arquivo logo.png não encontrado, continuando sem ícone...")
    
    # Compilar
    if build_executable():
        print("\n🎉 COMPILAÇÃO CONCLUÍDA!")
        print("📁 Verifique o arquivo Prospector.exe na pasta 'dist'")
        return True
    else:
        print("\n💥 FALHA NA COMPILAÇÃO!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
