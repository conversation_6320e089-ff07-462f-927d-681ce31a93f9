#!/usr/bin/env python3
"""
Script RÁPIDO para compilar o Prospector
Execute este arquivo para gerar o executável imediatamente
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    print("🚀 COMPILANDO PROSPECTOR - VERSÃO RÁPIDA")
    print("=" * 50)
    
    # Verificar se o arquivo principal existe
    if not os.path.exists('ui.py'):
        print("❌ Arquivo ui.py não encontrado!")
        input("Pressione Enter para sair...")
        return False
    
    print("📦 Instalando/atualizando PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller", "--upgrade"])
        print("✅ PyInstaller atualizado")
    except:
        print("⚠️ Erro ao atualizar PyInstaller, continuando...")
    
    print("\n🔨 Compilando executável...")
    
    # Comando otimizado do PyInstaller
    cmd = [
        'pyinstaller',
        '--onefile',                    # Arquivo único
        '--windowed',                   # Sem console
        '--name=Prospector',            # Nome do executável
        '--icon=logo.png',              # Ícone
        '--add-data=logo.png;.',        # Incluir logo
        '--noconfirm',                  # Não pedir confirmação
        '--clean',                      # Limpar cache
        
        # Imports importantes
        '--hidden-import=PyQt6.QtCore',
        '--hidden-import=PyQt6.QtGui',
        '--hidden-import=PyQt6.QtWidgets',
        '--hidden-import=selenium',
        '--hidden-import=webdriver_manager',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        
        'ui.py'                         # Arquivo principal
    ]
    
    print(f"📋 Executando: pyinstaller [opções] ui.py")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Compilação concluída!")
            
            # Verificar se o arquivo foi criado
            exe_path = Path("dist") / "Prospector.exe"
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Executável criado: {exe_path}")
                print(f"📏 Tamanho: {size_mb:.1f} MB")
                print("\n🎉 SUCESSO! Seu executável está pronto!")
                print("📂 Localização: dist/Prospector.exe")
                return True
            else:
                print("❌ Executável não foi encontrado após compilação")
                return False
        else:
            print("❌ Erro na compilação:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("✅ COMPILAÇÃO CONCLUÍDA COM SUCESSO!")
        print("🎯 Execute o arquivo: dist/Prospector.exe")
    else:
        print("❌ FALHA NA COMPILAÇÃO")
        print("💡 Tente executar: python build_prospector.py")
    
    input("\nPressione Enter para sair...")
