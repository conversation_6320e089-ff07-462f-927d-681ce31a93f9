#!/usr/bin/env python3
"""
Script otimizado para compilar o Prospector em executável
Versão melhorada com todas as dependências e configurações necessárias
"""

import os
import sys
import subprocess
import shutil
import platform
from datetime import datetime
from pathlib import Path

def print_header(text):
    """Imprime um cabeçalho formatado"""
    print("\n" + "=" * 70)
    print(f" {text} ".center(70, "="))
    print("=" * 70 + "\n")

def print_step(step, description):
    """Imprime um passo da compilação"""
    print(f"🔸 Passo {step}: {description}")

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    print_step(1, "Verificando versão do Python")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ é necessário")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detectado")
    return True

def check_and_install_dependencies():
    """Verifica e instala dependências necessárias"""
    print_step(2, "Verificando e instalando dependências")
    
    required_packages = [
        "pyinstaller>=5.6.0",
        "PyQt6>=6.0.0",
        "selenium>=4.1.0",
        "pandas>=1.3.0",
        "webdriver-manager>=3.5.0",
        "openpyxl>=3.0.9",
        "pillow>=9.0.0"
    ]
    
    if platform.system() == "Windows":
        required_packages.append("pywin32>=300")
    
    for package in required_packages:
        package_name = package.split(">=")[0].split("==")[0]
        try:
            __import__(package_name.replace("-", "_"))
            print(f"✅ {package_name} já está instalado")
        except ImportError:
            print(f"📦 Instalando {package}...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", package, "--upgrade"
                ])
                print(f"✅ {package_name} instalado com sucesso")
            except subprocess.CalledProcessError as e:
                print(f"❌ Erro ao instalar {package}: {e}")
                return False
    
    return True

def clean_previous_builds():
    """Limpa builds anteriores"""
    print_step(3, "Limpando builds anteriores")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🗑️ Removendo diretório {dir_name}...")
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Diretório {dir_name} removido")
            except Exception as e:
                print(f"⚠️ Aviso: Não foi possível remover {dir_name}: {e}")
    
    # Remover arquivos .spec
    for spec_file in Path('.').glob('*.spec'):
        try:
            spec_file.unlink()
            print(f"✅ Arquivo {spec_file} removido")
        except Exception as e:
            print(f"⚠️ Aviso: Não foi possível remover {spec_file}: {e}")
    
    print("✅ Limpeza concluída")

def verify_required_files():
    """Verifica se todos os arquivos necessários existem"""
    print_step(4, "Verificando arquivos necessários")
    
    required_files = {
        'ui.py': 'Arquivo principal da aplicação',
        'logo.png': 'Ícone da aplicação',
        'file_version_info.txt': 'Informações de versão',
        'logic_bot.py': 'Módulo de lógica',
        'splash_screen.py': 'Tela de splash',
        'about_dialog.py': 'Diálogo sobre',
        'resource_path.py': 'Gerenciador de recursos'
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        if os.path.exists(file_path):
            print(f"✅ {file_path} - {description}")
        else:
            print(f"❌ {file_path} - {description} (AUSENTE)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Arquivos ausentes: {', '.join(missing_files)}")
        return False
    
    return True

def build_executable():
    """Compila o executável com configurações otimizadas"""
    print_step(5, "Compilando executável")
    
    # Configurações do build
    main_file = "ui.py"
    output_name = "Prospector"
    
    # Arquivos de dados para incluir
    data_files = [
        ("logo.png", "."),
        ("file_version_info.txt", ".")
    ]
    
    # Construir parâmetros do PyInstaller
    cmd = [
        "pyinstaller",
        "--noconfirm",
        "--clean",
        "--onefile",
        "--windowed",
        f"--name={output_name}",
        f"--icon=logo.png",
        "--version-file=file_version_info.txt",
        
        # Otimizações
        "--noupx",
        "--disable-windowed-traceback",
        "--optimize=2",
        
        # Imports explícitos para PyQt6
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=PyQt6.QtNetwork",
        
        # Imports para Selenium
        "--hidden-import=selenium",
        "--hidden-import=selenium.webdriver",
        "--hidden-import=selenium.webdriver.chrome",
        "--hidden-import=selenium.webdriver.chrome.service",
        "--hidden-import=selenium.webdriver.chrome.options",
        "--hidden-import=selenium.webdriver.common.by",
        "--hidden-import=selenium.webdriver.support",
        "--hidden-import=selenium.webdriver.support.ui",
        "--hidden-import=selenium.webdriver.support.expected_conditions",
        
        # Outros imports
        "--hidden-import=webdriver_manager",
        "--hidden-import=webdriver_manager.chrome",
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        
        # Coletar todos os arquivos necessários
        "--collect-all=selenium",
        "--collect-all=webdriver_manager",
    ]
    
    # Adicionar arquivos de dados
    for src, dst in data_files:
        cmd.append(f"--add-data={src};{dst}")
    
    # Adicionar arquivo principal
    cmd.append(main_file)
    
    print(f"🔨 Executando PyInstaller...")
    print(f"📋 Comando: {' '.join(cmd[:10])}... (comando completo muito longo)")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Compilação concluída com sucesso!")
            return True
        else:
            print("❌ Erro na compilação:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação (>10 minutos)")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def verify_executable():
    """Verifica se o executável foi criado corretamente"""
    print_step(6, "Verificando executável gerado")
    
    exe_path = Path("dist") / "Prospector.exe"
    
    if not exe_path.exists():
        print(f"❌ Executável não encontrado: {exe_path}")
        return False
    
    # Verificar tamanho do arquivo
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    print(f"✅ Executável criado: {exe_path}")
    print(f"📏 Tamanho: {size_mb:.1f} MB")
    
    # Verificar data de criação
    mtime = datetime.fromtimestamp(exe_path.stat().st_mtime)
    print(f"🕒 Criado em: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return True

def main():
    """Função principal"""
    print_header("PROSPECTOR - COMPILADOR OTIMIZADO")
    print("🚀 Compilando aplicação para executável standalone")
    print(f"💻 Sistema: {platform.system()} {platform.release()}")
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Executar todas as etapas
    steps = [
        check_python_version,
        check_and_install_dependencies,
        clean_previous_builds,
        verify_required_files,
        build_executable,
        verify_executable
    ]
    
    for step_func in steps:
        if not step_func():
            print_header("❌ COMPILAÇÃO FALHOU")
            print("Verifique os erros acima e tente novamente.")
            return False
    
    print_header("🎉 COMPILAÇÃO CONCLUÍDA COM SUCESSO!")
    print("📁 Executável disponível em: dist/Prospector.exe")
    print("\n📋 Próximos passos:")
    print("1. Teste o executável em um ambiente limpo")
    print("2. Verifique se todas as funcionalidades estão funcionando")
    print("3. Distribua o arquivo dist/Prospector.exe")
    
    return True

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*50)
    if success:
        print("✅ Processo concluído com sucesso!")
    else:
        print("❌ Processo falhou. Verifique os erros acima.")
    
    input("\nPressione Enter para sair...")
    sys.exit(0 if success else 1)
